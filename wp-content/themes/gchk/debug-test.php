<?php

/**
 * Debug test file to check if the system is working
 * Access this file directly to test basic functionality
 */

// Include WordPress
require_once('../../../wp-config.php');

echo "<h1>Form System Debug Test</h1>";

// Test 1: Check if custom post type exists
echo "<h2>Test 1: Custom Post Type</h2>";
if (post_type_exists('form_entries')) {
    echo "✅ form_entries post type exists<br>";
} else {
    echo "❌ form_entries post type does not exist<br>";
}

// Test 2: Check if database table exists
echo "<h2>Test 2: Database Table</h2>";
global $wpdb;
$table_name = $wpdb->prefix . 'user_auth_keys';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "✅ user_auth_keys table exists<br>";
} else {
    echo "❌ user_auth_keys table does not exist<br>";
    echo "Attempting to create table...<br>";

    // Try to create the table
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        login_key varchar(16) NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        expires_at datetime NOT NULL,
        last_access datetime NULL,
        is_active tinyint(1) DEFAULT 1,
        PRIMARY KEY (id),
        UNIQUE KEY login_key (login_key),
        KEY user_id (user_id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Check again
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    if ($table_exists) {
        echo "✅ Table created successfully<br>";
    } else {
        echo "❌ Failed to create table<br>";
    }
}

// Test 3: Check if functions exist
echo "<h2>Test 3: Required Functions</h2>";
$functions_to_check = [
    'generate_qr_code',
    'generate_login_key',
    'check_duplicate_entry',
    'get_email_template'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ Function $function exists<br>";
    } else {
        echo "❌ Function $function does not exist<br>";
    }
}

// Test 4: Check if pages exist
echo "<h2>Test 4: Required Pages</h2>";
$login_page = get_page_by_path('login');
$submissions_page = get_page_by_path('view-submissions');

if ($login_page) {
    echo "✅ Login page exists (ID: " . $login_page->ID . ")<br>";
} else {
    echo "❌ Login page does not exist<br>";
}

if ($submissions_page) {
    echo "✅ View submissions page exists (ID: " . $submissions_page->ID . ")<br>";
} else {
    echo "❌ View submissions page does not exist<br>";
}

// Test 5: Test QR code generation
echo "<h2>Test 5: QR Code Generation</h2>";
if (function_exists('generate_qr_code')) {
    $test_url = home_url('/login?user_id=123');
    $qr_result = generate_qr_code($test_url, 'test_qr.png');

    if ($qr_result) {
        echo "✅ QR code generated successfully: <a href='$qr_result' target='_blank'>$qr_result</a><br>";
        echo "<img src='$qr_result' alt='Test QR Code' style='max-width: 200px; border: 1px solid #ccc; margin: 10px 0;'><br>";
    } else {
        echo "❌ QR code generation failed<br>";
    }
} else {
    echo "❌ QR code function not available<br>";
}

// Test 5.1: Check if Composer autoloader is working
echo "<h3>Test 5.1: Composer Dependencies</h3>";
if (class_exists('Endroid\\QrCode\\QrCode')) {
    echo "✅ Endroid QR Code library loaded successfully<br>";
} else {
    echo "❌ Endroid QR Code library not found<br>";
}

// Test 6: Test login key generation
echo "<h2>Test 6: Login Key Generation</h2>";
if (function_exists('generate_login_key')) {
    $test_key = generate_login_key();
    echo "✅ Login key generated: $test_key<br>";
} else {
    echo "❌ Login key function not available<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>If you see any ❌ errors above, those need to be fixed before the system will work properly.</p>";
