<?php

/**
 * Template Name: View Submissions
 */

// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user_id is provided
if (!isset($_GET['user_id'])) {
    wp_redirect(home_url('/login'));
    exit;
}

$user_id = intval($_GET['user_id']);

// Check authentication and session timeout
if (
    !isset($_SESSION['authenticated_user_id']) ||
    $_SESSION['authenticated_user_id'] != $user_id
) {
    wp_redirect(home_url('/login?user_id=' . $user_id));
    exit;
}

// Check 15-minute timeout
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 900) { // 15 minutes = 900 seconds
    session_destroy();
    wp_redirect(home_url('/login?timeout=1&user_id=' . $user_id));
    exit;
}

// Update last activity time
$_SESSION['login_time'] = time();

// Get user's form submission
$post = get_post($user_id);
if (!$post || $post->post_type !== 'form_entries') {
    wp_die('Invalid submission ID');
}

get_header(); ?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f8f9fa;
        margin: 0;
        padding: 20px;
        /* Disable text selection */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        /* Disable image dragging */
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        user-drag: none;
    }

    .submissions-container {
        max-width: 1000px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .submissions-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .submissions-header h1 {
        margin: 0;
        font-size: 28px;
    }

    .submissions-content {
        padding: 40px;
    }

    .section {
        margin-bottom: 40px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        overflow: hidden;
    }

    .section-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .section-header h3 {
        margin: 0;
        color: #495057;
        font-size: 18px;
    }

    .section-content {
        padding: 20px;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .data-table th {
        background: #f8f9fa;
        font-weight: bold;
        color: #495057;
        width: 30%;
    }

    .data-table td {
        color: #6c757d;
    }

    .document-preview {
        max-width: 200px;
        max-height: 200px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-top: 10px;
    }

    .logout-button {
        background: #dc3545;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        margin-bottom: 20px;
    }

    .logout-button:hover {
        background: #c82333;
        color: white;
        text-decoration: none;
    }

    .session-timer {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        color: #856404;
        padding: 10px 15px;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
        font-size: 14px;
        z-index: 1000;
    }

    .watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 100px;
        color: rgba(0, 0, 0, 0.05);
        pointer-events: none;
        z-index: -1;
        font-weight: bold;
    }
</style>

<div class="watermark">CONFIDENTIAL</div>

<div class="session-timer" id="sessionTimer">
    Session expires in: <span id="timeLeft">15:00</span>
</div>

<div class="submissions-container">
    <div class="submissions-header">
        <h1>📋 Your Form Submission</h1>
        <p>Secure access to your submitted information</p>
    </div>

    <div class="submissions-content">
        <a href="<?php echo home_url('/login'); ?>" class="logout-button">🔓 Logout</a>

        <!-- Personal Information -->
        <div class="section">
            <div class="section-header">
                <h3>Personal Information</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Full Name</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_full_name', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Date of Birth</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_birth_date', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Gender</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_gender', true)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="section">
            <div class="section-header">
                <h3>Contact Information</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Phone Number</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_phone_number', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Email Address</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_email_address', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_address', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Country of Residence</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_country_residence', true)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Government Identification -->
        <div class="section">
            <div class="section-header">
                <h3>Government Identification</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Identification Number</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_identification_number', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Issuing Country</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_issuing_country', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Expiration Date</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_expiration_date', true)); ?></td>
                    </tr>
                    <tr>
                        <th>National ID Number</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_id_number', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Passport Number</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_passport_number', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Place of Issue</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_place_issue', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Date of Issue</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_date_issue', true)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Emergency Contact -->
        <div class="section">
            <div class="section-header">
                <h3>Emergency Contact</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Contact Name</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_contact_name', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Relationship</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_contact_relationship', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Phone Number</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_contact_number', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Email Address</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_contact_email', true)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="section">
            <div class="section-header">
                <h3>Professional Information</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Occupation</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_occupation', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Employer</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_employer', true)); ?></td>
                    </tr>
                    <tr>
                        <th>Professional Affiliations</th>
                        <td><?php echo esc_html(get_post_meta($user_id, '_professional_aff', true)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Documents -->
        <div class="section">
            <div class="section-header">
                <h3>Uploaded Documents</h3>
            </div>
            <div class="section-content">
                <table class="data-table">
                    <tr>
                        <th>Passport Photo</th>
                        <td>
                            <?php
                            $photo_url = get_post_meta($user_id, '_photo_url', true);
                            if ($photo_url): ?>
                                <img src="<?php echo esc_url($photo_url); ?>" class="document-preview" alt="Passport Photo">
                            <?php else: ?>
                                <em>No photo uploaded</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Digital Signature</th>
                        <td>
                            <?php
                            $signature_url = get_post_meta($user_id, '_signature_url', true);
                            if ($signature_url): ?>
                                <img src="<?php echo esc_url($signature_url); ?>" class="document-preview" alt="Digital Signature">
                            <?php else: ?>
                                <em>No signature uploaded</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    // Session timeout countdown
    let timeLeft = 900; // 15 minutes in seconds

    function updateTimer() {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        document.getElementById('timeLeft').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        if (timeLeft <= 0) {
            alert('Your session has expired for security reasons. You will be redirected to the login page.');
            window.location.href = '<?php echo home_url('/login?timeout=1'); ?>';
            return;
        }

        timeLeft--;
    }

    // Update timer every second
    setInterval(updateTimer, 1000);

    // Security measures
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    document.addEventListener('keydown', function(e) {
        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Print Screen
        if (e.keyCode == 123 || e.keyCode == 44 ||
            (e.ctrlKey && e.shiftKey && e.keyCode == 73) ||
            (e.ctrlKey && e.shiftKey && e.keyCode == 74) ||
            (e.ctrlKey && e.keyCode == 85)) {
            e.preventDefault();
            return false;
        }
    });

    // Disable print
    window.addEventListener('beforeprint', function(e) {
        e.preventDefault();
        alert('Printing is disabled for security reasons.');
        return false;
    });

    // Refresh session on user activity
    document.addEventListener('click', function() {
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=refresh_session&user_id=<?php echo $user_id; ?>'
        });
        timeLeft = 900; // Reset timer
    });

    // Disable image saving
    document.addEventListener('dragstart', function(e) {
        if (e.target.tagName === 'IMG') {
            e.preventDefault();
        }
    });
</script>

<?php get_footer(); ?>