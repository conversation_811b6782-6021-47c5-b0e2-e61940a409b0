<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'BaconQrCode\\Common\\BitArray' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitArray.php',
    'BaconQrCode\\Common\\BitMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitMatrix.php',
    'BaconQrCode\\Common\\BitUtils' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitUtils.php',
    'BaconQrCode\\Common\\CharacterSetEci' => $vendorDir . '/bacon/bacon-qr-code/src/Common/CharacterSetEci.php',
    'BaconQrCode\\Common\\EcBlock' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlock.php',
    'BaconQrCode\\Common\\EcBlocks' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlocks.php',
    'BaconQrCode\\Common\\ErrorCorrectionLevel' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ErrorCorrectionLevel.php',
    'BaconQrCode\\Common\\FormatInformation' => $vendorDir . '/bacon/bacon-qr-code/src/Common/FormatInformation.php',
    'BaconQrCode\\Common\\Mode' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Mode.php',
    'BaconQrCode\\Common\\ReedSolomonCodec' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ReedSolomonCodec.php',
    'BaconQrCode\\Common\\Version' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Version.php',
    'BaconQrCode\\Encoder\\BlockPair' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/BlockPair.php',
    'BaconQrCode\\Encoder\\ByteMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/ByteMatrix.php',
    'BaconQrCode\\Encoder\\Encoder' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/Encoder.php',
    'BaconQrCode\\Encoder\\MaskUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MaskUtil.php',
    'BaconQrCode\\Encoder\\MatrixUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MatrixUtil.php',
    'BaconQrCode\\Encoder\\QrCode' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/QrCode.php',
    'BaconQrCode\\Exception\\ExceptionInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/ExceptionInterface.php',
    'BaconQrCode\\Exception\\InvalidArgumentException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/InvalidArgumentException.php',
    'BaconQrCode\\Exception\\OutOfBoundsException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/OutOfBoundsException.php',
    'BaconQrCode\\Exception\\RuntimeException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/RuntimeException.php',
    'BaconQrCode\\Exception\\UnexpectedValueException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/UnexpectedValueException.php',
    'BaconQrCode\\Exception\\WriterException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/WriterException.php',
    'BaconQrCode\\Renderer\\Color\\Alpha' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Alpha.php',
    'BaconQrCode\\Renderer\\Color\\Cmyk' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Cmyk.php',
    'BaconQrCode\\Renderer\\Color\\ColorInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/ColorInterface.php',
    'BaconQrCode\\Renderer\\Color\\Gray' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Gray.php',
    'BaconQrCode\\Renderer\\Color\\Rgb' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Rgb.php',
    'BaconQrCode\\Renderer\\Eye\\CompositeEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/CompositeEye.php',
    'BaconQrCode\\Renderer\\Eye\\EyeInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/EyeInterface.php',
    'BaconQrCode\\Renderer\\Eye\\ModuleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/ModuleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SimpleCircleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SimpleCircleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SquareEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SquareEye.php',
    'BaconQrCode\\Renderer\\ImageRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/ImageRenderer.php',
    'BaconQrCode\\Renderer\\Image\\EpsImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/EpsImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\ImageBackEndInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImageBackEndInterface.php',
    'BaconQrCode\\Renderer\\Image\\ImagickImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImagickImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\SvgImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/SvgImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\TransformationMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/TransformationMatrix.php',
    'BaconQrCode\\Renderer\\Module\\DotsModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/DotsModule.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\Edge' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/Edge.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\EdgeIterator' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/EdgeIterator.php',
    'BaconQrCode\\Renderer\\Module\\ModuleInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/ModuleInterface.php',
    'BaconQrCode\\Renderer\\Module\\RoundnessModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/RoundnessModule.php',
    'BaconQrCode\\Renderer\\Module\\SquareModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/SquareModule.php',
    'BaconQrCode\\Renderer\\Path\\Close' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Close.php',
    'BaconQrCode\\Renderer\\Path\\Curve' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Curve.php',
    'BaconQrCode\\Renderer\\Path\\EllipticArc' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/EllipticArc.php',
    'BaconQrCode\\Renderer\\Path\\Line' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Line.php',
    'BaconQrCode\\Renderer\\Path\\Move' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Move.php',
    'BaconQrCode\\Renderer\\Path\\OperationInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/OperationInterface.php',
    'BaconQrCode\\Renderer\\Path\\Path' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Path.php',
    'BaconQrCode\\Renderer\\PlainTextRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/PlainTextRenderer.php',
    'BaconQrCode\\Renderer\\RendererInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererInterface.php',
    'BaconQrCode\\Renderer\\RendererStyle\\EyeFill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/EyeFill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Fill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Fill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Gradient' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Gradient.php',
    'BaconQrCode\\Renderer\\RendererStyle\\GradientType' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/GradientType.php',
    'BaconQrCode\\Renderer\\RendererStyle\\RendererStyle' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/RendererStyle.php',
    'BaconQrCode\\Writer' => $vendorDir . '/bacon/bacon-qr-code/src/Writer.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DASPRiD\\Enum\\AbstractEnum' => $vendorDir . '/dasprid/enum/src/AbstractEnum.php',
    'DASPRiD\\Enum\\EnumMap' => $vendorDir . '/dasprid/enum/src/EnumMap.php',
    'DASPRiD\\Enum\\Exception\\CloneNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/CloneNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\ExceptionInterface' => $vendorDir . '/dasprid/enum/src/Exception/ExceptionInterface.php',
    'DASPRiD\\Enum\\Exception\\ExpectationException' => $vendorDir . '/dasprid/enum/src/Exception/ExpectationException.php',
    'DASPRiD\\Enum\\Exception\\IllegalArgumentException' => $vendorDir . '/dasprid/enum/src/Exception/IllegalArgumentException.php',
    'DASPRiD\\Enum\\Exception\\MismatchException' => $vendorDir . '/dasprid/enum/src/Exception/MismatchException.php',
    'DASPRiD\\Enum\\Exception\\SerializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/SerializeNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\UnserializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/UnserializeNotSupportedException.php',
    'DASPRiD\\Enum\\NullValue' => $vendorDir . '/dasprid/enum/src/NullValue.php',
    'Endroid\\QrCode\\Bacon\\ErrorCorrectionLevelConverter' => $vendorDir . '/endroid/qr-code/src/Bacon/ErrorCorrectionLevelConverter.php',
    'Endroid\\QrCode\\Bacon\\MatrixFactory' => $vendorDir . '/endroid/qr-code/src/Bacon/MatrixFactory.php',
    'Endroid\\QrCode\\Builder\\Builder' => $vendorDir . '/endroid/qr-code/src/Builder/Builder.php',
    'Endroid\\QrCode\\Builder\\BuilderInterface' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderInterface.php',
    'Endroid\\QrCode\\Builder\\BuilderRegistry' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderRegistry.php',
    'Endroid\\QrCode\\Builder\\BuilderRegistryInterface' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderRegistryInterface.php',
    'Endroid\\QrCode\\Color\\Color' => $vendorDir . '/endroid/qr-code/src/Color/Color.php',
    'Endroid\\QrCode\\Color\\ColorInterface' => $vendorDir . '/endroid/qr-code/src/Color/ColorInterface.php',
    'Endroid\\QrCode\\Encoding\\Encoding' => $vendorDir . '/endroid/qr-code/src/Encoding/Encoding.php',
    'Endroid\\QrCode\\Encoding\\EncodingInterface' => $vendorDir . '/endroid/qr-code/src/Encoding/EncodingInterface.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelHigh' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelHigh.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelInterface' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelInterface.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelLow' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelLow.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelMedium' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelMedium.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelQuartile' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelQuartile.php',
    'Endroid\\QrCode\\Exception\\ValidationException' => $vendorDir . '/endroid/qr-code/src/Exception/ValidationException.php',
    'Endroid\\QrCode\\ImageData\\LabelImageData' => $vendorDir . '/endroid/qr-code/src/ImageData/LabelImageData.php',
    'Endroid\\QrCode\\ImageData\\LogoImageData' => $vendorDir . '/endroid/qr-code/src/ImageData/LogoImageData.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentCenter' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentCenter.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentInterface' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentInterface.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentLeft' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentLeft.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentRight' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentRight.php',
    'Endroid\\QrCode\\Label\\Font\\Font' => $vendorDir . '/endroid/qr-code/src/Label/Font/Font.php',
    'Endroid\\QrCode\\Label\\Font\\FontInterface' => $vendorDir . '/endroid/qr-code/src/Label/Font/FontInterface.php',
    'Endroid\\QrCode\\Label\\Font\\NotoSans' => $vendorDir . '/endroid/qr-code/src/Label/Font/NotoSans.php',
    'Endroid\\QrCode\\Label\\Font\\OpenSans' => $vendorDir . '/endroid/qr-code/src/Label/Font/OpenSans.php',
    'Endroid\\QrCode\\Label\\Label' => $vendorDir . '/endroid/qr-code/src/Label/Label.php',
    'Endroid\\QrCode\\Label\\LabelInterface' => $vendorDir . '/endroid/qr-code/src/Label/LabelInterface.php',
    'Endroid\\QrCode\\Label\\Margin\\Margin' => $vendorDir . '/endroid/qr-code/src/Label/Margin/Margin.php',
    'Endroid\\QrCode\\Label\\Margin\\MarginInterface' => $vendorDir . '/endroid/qr-code/src/Label/Margin/MarginInterface.php',
    'Endroid\\QrCode\\Logo\\Logo' => $vendorDir . '/endroid/qr-code/src/Logo/Logo.php',
    'Endroid\\QrCode\\Logo\\LogoInterface' => $vendorDir . '/endroid/qr-code/src/Logo/LogoInterface.php',
    'Endroid\\QrCode\\Matrix\\Matrix' => $vendorDir . '/endroid/qr-code/src/Matrix/Matrix.php',
    'Endroid\\QrCode\\Matrix\\MatrixFactoryInterface' => $vendorDir . '/endroid/qr-code/src/Matrix/MatrixFactoryInterface.php',
    'Endroid\\QrCode\\Matrix\\MatrixInterface' => $vendorDir . '/endroid/qr-code/src/Matrix/MatrixInterface.php',
    'Endroid\\QrCode\\QrCode' => $vendorDir . '/endroid/qr-code/src/QrCode.php',
    'Endroid\\QrCode\\QrCodeInterface' => $vendorDir . '/endroid/qr-code/src/QrCodeInterface.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeEnlarge' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeEnlarge.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeInterface' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeInterface.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeMargin' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeMargin.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeNone' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeNone.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeShrink' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeShrink.php',
    'Endroid\\QrCode\\Writer\\AbstractGdWriter' => $vendorDir . '/endroid/qr-code/src/Writer/AbstractGdWriter.php',
    'Endroid\\QrCode\\Writer\\BinaryWriter' => $vendorDir . '/endroid/qr-code/src/Writer/BinaryWriter.php',
    'Endroid\\QrCode\\Writer\\ConsoleWriter' => $vendorDir . '/endroid/qr-code/src/Writer/ConsoleWriter.php',
    'Endroid\\QrCode\\Writer\\DebugWriter' => $vendorDir . '/endroid/qr-code/src/Writer/DebugWriter.php',
    'Endroid\\QrCode\\Writer\\EpsWriter' => $vendorDir . '/endroid/qr-code/src/Writer/EpsWriter.php',
    'Endroid\\QrCode\\Writer\\GifWriter' => $vendorDir . '/endroid/qr-code/src/Writer/GifWriter.php',
    'Endroid\\QrCode\\Writer\\PdfWriter' => $vendorDir . '/endroid/qr-code/src/Writer/PdfWriter.php',
    'Endroid\\QrCode\\Writer\\PngWriter' => $vendorDir . '/endroid/qr-code/src/Writer/PngWriter.php',
    'Endroid\\QrCode\\Writer\\Result\\AbstractResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/AbstractResult.php',
    'Endroid\\QrCode\\Writer\\Result\\BinaryResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/BinaryResult.php',
    'Endroid\\QrCode\\Writer\\Result\\ConsoleResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/ConsoleResult.php',
    'Endroid\\QrCode\\Writer\\Result\\DebugResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/DebugResult.php',
    'Endroid\\QrCode\\Writer\\Result\\EpsResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/EpsResult.php',
    'Endroid\\QrCode\\Writer\\Result\\GdResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/GdResult.php',
    'Endroid\\QrCode\\Writer\\Result\\GifResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/GifResult.php',
    'Endroid\\QrCode\\Writer\\Result\\PdfResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/PdfResult.php',
    'Endroid\\QrCode\\Writer\\Result\\PngResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/PngResult.php',
    'Endroid\\QrCode\\Writer\\Result\\ResultInterface' => $vendorDir . '/endroid/qr-code/src/Writer/Result/ResultInterface.php',
    'Endroid\\QrCode\\Writer\\Result\\SvgResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/SvgResult.php',
    'Endroid\\QrCode\\Writer\\Result\\WebPResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/WebPResult.php',
    'Endroid\\QrCode\\Writer\\SvgWriter' => $vendorDir . '/endroid/qr-code/src/Writer/SvgWriter.php',
    'Endroid\\QrCode\\Writer\\ValidatingWriterInterface' => $vendorDir . '/endroid/qr-code/src/Writer/ValidatingWriterInterface.php',
    'Endroid\\QrCode\\Writer\\WebPWriter' => $vendorDir . '/endroid/qr-code/src/Writer/WebPWriter.php',
    'Endroid\\QrCode\\Writer\\WriterInterface' => $vendorDir . '/endroid/qr-code/src/Writer/WriterInterface.php',
);
